import React, { useState, useEffect } from "react"
import { BarChart3, Download, Upload, Trash2, RefreshCw } from "lucide-react"

import { Button } from "@src/components/ui"
import { SectionHeader } from "./SectionHeader"
import { Section } from "./Section"

// Import usage tracking functions (these will be available in the webview context)
declare global {
	interface Window {
		usageTracking: {
			getUsageStats(): {
				totalCubentUnits: number
				totalMessages: number
				entries: Array<{
					timestamp: number
					modelId: string
					cubentUnits: number
					messageCount: number
					provider: string
					configName: string
				}>
				lastUpdated: number
			}
			getUsageStatsForPeriod(days: number): {
				totalCubentUnits: number
				totalMessages: number
				entries: Array<{
					timestamp: number
					modelId: string
					cubentUnits: number
					messageCount: number
					provider: string
					configName: string
				}>
				lastUpdated: number
			}
			getUsageByModel(days?: number): Record<string, { cubentUnits: number; messages: number }>
			clearUsageStats(): void
			exportUsageData(): string
			importUsageData(jsonData: string): boolean
		}
	}
}

interface UsageSettingsProps {
	// No props needed for now
}

export const UsageSettings: React.FC<UsageSettingsProps> = () => {
	const [usageStats, setUsageStats] = useState({
		totalCubentUnits: 0,
		totalMessages: 0,
		entries: [],
		lastUpdated: Date.now(),
	})
	const [selectedPeriod, setSelectedPeriod] = useState<number | null>(null) // null = all time
	const [modelBreakdown, setModelBreakdown] = useState<Record<string, { cubentUnits: number; messages: number }>>({})
	const [isLoading, setIsLoading] = useState(false)

	// Load usage stats
	const loadUsageStats = () => {
		if (window.usageTracking) {
			const stats = selectedPeriod 
				? window.usageTracking.getUsageStatsForPeriod(selectedPeriod)
				: window.usageTracking.getUsageStats()
			
			setUsageStats(stats)
			
			const breakdown = window.usageTracking.getUsageByModel(selectedPeriod || undefined)
			setModelBreakdown(breakdown)
		}
	}

	// Load stats on component mount and when period changes
	useEffect(() => {
		loadUsageStats()
	}, [selectedPeriod])

	// Refresh stats
	const handleRefresh = () => {
		setIsLoading(true)
		setTimeout(() => {
			loadUsageStats()
			setIsLoading(false)
		}, 100)
	}

	// Clear all usage data
	const handleClearData = () => {
		if (window.confirm("Are you sure you want to clear all usage data? This action cannot be undone.")) {
			if (window.usageTracking) {
				window.usageTracking.clearUsageStats()
				loadUsageStats()
			}
		}
	}

	// Export usage data
	const handleExport = () => {
		if (window.usageTracking) {
			const data = window.usageTracking.exportUsageData()
			const blob = new Blob([data], { type: "application/json" })
			const url = URL.createObjectURL(blob)
			const a = document.createElement("a")
			a.href = url
			a.download = `cubent-usage-${new Date().toISOString().split('T')[0]}.json`
			document.body.appendChild(a)
			a.click()
			document.body.removeChild(a)
			URL.revokeObjectURL(url)
		}
	}

	// Import usage data
	const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0]
		if (file && window.usageTracking) {
			const reader = new FileReader()
			reader.onload = (e) => {
				const content = e.target?.result as string
				if (content) {
					const success = window.usageTracking.importUsageData(content)
					if (success) {
						loadUsageStats()
						alert("Usage data imported successfully!")
					} else {
						alert("Failed to import usage data. Please check the file format.")
					}
				}
			}
			reader.readAsText(file)
		}
		// Reset the input
		event.target.value = ""
	}

	// Format date
	const formatDate = (timestamp: number) => {
		return new Date(timestamp).toLocaleDateString()
	}

	// Get top models by usage
	const topModels = Object.entries(modelBreakdown)
		.sort(([, a], [, b]) => b.cubentUnits - a.cubentUnits)
		.slice(0, 5)

	return (
		<div>
			<SectionHeader>
				<div className="flex items-center gap-2">
					<BarChart3 className="w-4" />
					<div>Cubent Units Usage</div>
				</div>
			</SectionHeader>

			<Section>
				{/* Overview Stats */}
				<div className="space-y-4">
					<div className="flex items-center justify-between">
						<h4 className="text-sm font-medium text-vscode-foreground">Usage Overview</h4>
						<div className="flex items-center gap-2">
							<Button
								variant="outline"
								size="sm"
								onClick={handleRefresh}
								disabled={isLoading}
								className="flex items-center gap-1">
								<RefreshCw className={`w-3 h-3 ${isLoading ? 'animate-spin' : ''}`} />
								Refresh
							</Button>
						</div>
					</div>

					{/* Period Selector */}
					<div className="flex items-center gap-2 text-sm">
						<span className="text-vscode-descriptionForeground">Time period:</span>
						<select
							value={selectedPeriod || "all"}
							onChange={(e) => setSelectedPeriod(e.target.value === "all" ? null : parseInt(e.target.value))}
							className="bg-vscode-input-background text-vscode-input-foreground border border-vscode-input-border rounded px-2 py-1">
							<option value="all">All time</option>
							<option value="1">Last 24 hours</option>
							<option value="7">Last 7 days</option>
							<option value="30">Last 30 days</option>
						</select>
					</div>

					{/* Stats Cards */}
					<div className="grid grid-cols-2 gap-4">
						<div className="bg-vscode-editor-background border border-vscode-panel-border rounded-lg p-4">
							<div className="text-2xl font-bold text-vscode-foreground">
								{usageStats.totalCubentUnits.toFixed(2)}
							</div>
							<div className="text-sm text-vscode-descriptionForeground">Total Cubent Units</div>
						</div>
						<div className="bg-vscode-editor-background border border-vscode-panel-border rounded-lg p-4">
							<div className="text-2xl font-bold text-vscode-foreground">
								{usageStats.totalMessages.toLocaleString()}
							</div>
							<div className="text-sm text-vscode-descriptionForeground">Messages Sent</div>
						</div>
					</div>

					{/* Top Models */}
					{topModels.length > 0 && (
						<div>
							<h5 className="text-sm font-medium text-vscode-foreground mb-2">Top Models by Usage</h5>
							<div className="space-y-2">
								{topModels.map(([modelId, stats]) => (
									<div key={modelId} className="flex items-center justify-between text-sm">
										<span className="text-vscode-foreground font-mono">{modelId}</span>
										<div className="flex items-center gap-4 text-vscode-descriptionForeground">
											<span>{stats.cubentUnits.toFixed(2)} units</span>
											<span>{stats.messages} msgs</span>
										</div>
									</div>
								))}
							</div>
						</div>
					)}

					{/* Last Updated */}
					{usageStats.lastUpdated && (
						<div className="text-xs text-vscode-descriptionForeground">
							Last updated: {formatDate(usageStats.lastUpdated)}
						</div>
					)}
				</div>
			</Section>

			{/* Data Management */}
			<Section>
				<div className="space-y-4">
					<h4 className="text-sm font-medium text-vscode-foreground">Data Management</h4>
					
					<div className="flex items-center gap-2">
						<Button
							variant="outline"
							size="sm"
							onClick={handleExport}
							className="flex items-center gap-1">
							<Download className="w-3 h-3" />
							Export Data
						</Button>
						
						<label className="cursor-pointer">
							<Button
								variant="outline"
								size="sm"
								className="flex items-center gap-1"
								asChild>
								<span>
									<Upload className="w-3 h-3" />
									Import Data
								</span>
							</Button>
							<input
								type="file"
								accept=".json"
								onChange={handleImport}
								className="hidden"
							/>
						</label>
						
						<Button
							variant="outline"
							size="sm"
							onClick={handleClearData}
							className="flex items-center gap-1 text-red-600 hover:text-red-700">
							<Trash2 className="w-3 h-3" />
							Clear Data
						</Button>
					</div>
					
					<div className="text-xs text-vscode-descriptionForeground">
						Usage data is stored locally in your browser. Export your data to back it up or transfer it to another device.
					</div>
				</div>
			</Section>
		</div>
	)
}
