// Usage tracking utilities for the webview
// This mirrors the functionality from the shared usage-tracking.ts but adapted for the webview context

// Cubent Units mapping based on https://cubentdev.mintlify.app/models-and-pricing
const CUBENT_UNITS_MAPPING: Record<string, number> = {
	// Anthropic Claude Models
	"claude-3-7-sonnet-20250219": 1.1,
	"claude-3-7-sonnet-thinking-20250219": 1.35,
	"claude-3-5-sonnet-20241022": 0.95,
	"claude-3-5-haiku-20241022": 0.55,
	"claude-3-haiku-20240307": 0.45,

	// OpenAI Models
	"gpt-4o": 1.1,
	"gpt-4.5-preview": 1.2,
	"gpt-4o-mini": 0.65,
	"o3-mini": 1.0,
	"o3-mini-high": 1.1,
	"o3-mini-low": 0.75,

	// DeepSeek Models
	"deepseek-chat": 0.35,
	"deepseek-reasoner": 0.7,

	// Google Gemini Models
	"gemini-2.5-flash": 0.3,
	"gemini-2.5-flash-thinking": 0.4,
	"gemini-2.5-pro": 0.85,
	"gemini-2.0-flash": 0.45,
	"gemini-2.0-pro": 0.70,
	"gemini-1.5-flash": 0.40,
	"gemini-1.5-pro": 0.65,

	// xAI Grok Models
	"grok-3": 1.1,
	"grok-3-mini": 0.30,
	"grok-2-vision": 0.70,
}

// Usage tracking data structure
export interface UsageEntry {
	timestamp: number
	modelId: string
	cubentUnits: number
	messageCount: number
	provider: string
	configName: string
}

export interface UsageStats {
	totalCubentUnits: number
	totalMessages: number
	entries: UsageEntry[]
	lastUpdated: number
}

// Local storage key for usage data
const USAGE_STORAGE_KEY = "cubent-usage-stats"

// Normalize model IDs to match our mapping (handles provider prefixes and variations)
function normalizeModelId(modelId: string): string {
	// Remove common provider prefixes
	let normalized = modelId
		.replace(/^anthropic\//, "")
		.replace(/^openai\//, "")
		.replace(/^google\//, "")
		.replace(/^deepseek\//, "")
		.replace(/^xai\//, "")
		.toLowerCase()

	// Remove version suffixes (like -001, -002, -beta, -exp, etc.)
	normalized = normalized
		.replace(/-\d{3}$/, "") // Remove -001, -002, etc.
		.replace(/-beta$/, "")
		.replace(/-exp$/, "")
		.replace(/-experimental$/, "")
		.replace(/-preview$/, "")
		.replace(/-latest$/, "")

	// Handle common model ID variations
	const mappings: Record<string, string> = {
		// Claude variations
		"claude-sonnet-4-20250514": "claude-3-7-sonnet-20250219",
		"claude-3.7-sonnet": "claude-3-7-sonnet-20250219",
		"claude-3.7-sonnet-thinking": "claude-3-7-sonnet-thinking-20250219",
		"claude-3.5-sonnet": "claude-3-5-sonnet-20241022",
		"claude-3.5-haiku": "claude-3-5-haiku-20241022",
		"claude-3-haiku": "claude-3-haiku-20240307",

		// OpenAI variations
		"gpt-4o-2024-11-20": "gpt-4o",
		"gpt-4o-mini-2024-07-18": "gpt-4o-mini",
		"o3-mini-high-reasoning": "o3-mini-high",
		"o3-mini-low-reasoning": "o3-mini-low",

		// Gemini variations
		"gemini-2.5-flash-002": "gemini-2.5-flash",
		"gemini-2.5-flash-thinking-exp": "gemini-2.5-flash-thinking",
		"gemini-2.5-pro-002": "gemini-2.5-pro",
		"gemini-2.0-flash-exp": "gemini-2.0-flash",

		// DeepSeek variations
		"deepseek-ai/deepseek-chat": "deepseek-chat",
		"deepseek-ai/deepseek-reasoner": "deepseek-reasoner",

		// Grok variations
		"grok-3-beta": "grok-3",
		"grok-3-mini-beta": "grok-3-mini",
		"grok-2-vision-beta": "grok-2-vision",
	}

	return mappings[normalized] || normalized
}

// Get Cubent units for a model (returns 0 for BYAK models or unknown models)
function getCubentUnitsForModel(modelId: string): number {
	const normalizedModelId = normalizeModelId(modelId)
	return CUBENT_UNITS_MAPPING[normalizedModelId] || 0
}

// Get current usage stats from local storage
export function getUsageStats(): UsageStats {
	try {
		const stored = localStorage.getItem(USAGE_STORAGE_KEY)
		if (stored) {
			const parsed = JSON.parse(stored) as UsageStats
			// Ensure the structure is valid
			return {
				totalCubentUnits: parsed.totalCubentUnits || 0,
				totalMessages: parsed.totalMessages || 0,
				entries: Array.isArray(parsed.entries) ? parsed.entries : [],
				lastUpdated: parsed.lastUpdated || Date.now(),
			}
		}
	} catch (error) {
		console.warn("Failed to parse usage stats from localStorage:", error)
	}

	// Return default stats if nothing stored or parsing failed
	return {
		totalCubentUnits: 0,
		totalMessages: 0,
		entries: [],
		lastUpdated: Date.now(),
	}
}

// Save usage stats to local storage
export function saveUsageStats(stats: UsageStats): void {
	try {
		localStorage.setItem(USAGE_STORAGE_KEY, JSON.stringify(stats))
	} catch (error) {
		console.error("Failed to save usage stats to localStorage:", error)
	}
}

// Track a new message usage
export function trackMessageUsage(
	modelId: string,
	provider: string,
	configName: string,
	messageCount: number = 1,
): void {
	const cubentUnits = getCubentUnitsForModel(modelId)

	// Only track if the model has Cubent units (non-BYAK models)
	if (cubentUnits > 0) {
		const stats = getUsageStats()

		const entry: UsageEntry = {
			timestamp: Date.now(),
			modelId,
			cubentUnits: cubentUnits * messageCount,
			messageCount,
			provider,
			configName,
		}

		stats.entries.push(entry)
		stats.totalCubentUnits += entry.cubentUnits
		stats.totalMessages += messageCount
		stats.lastUpdated = Date.now()

		// Keep only last 1000 entries to prevent storage bloat
		if (stats.entries.length > 1000) {
			const removedEntries = stats.entries.splice(0, stats.entries.length - 1000)
			// Recalculate totals to maintain accuracy
			stats.totalCubentUnits = stats.entries.reduce((sum, e) => sum + e.cubentUnits, 0)
			stats.totalMessages = stats.entries.reduce((sum, e) => sum + e.messageCount, 0)
		}

		saveUsageStats(stats)
	}
}

// Get usage stats for a specific time period
export function getUsageStatsForPeriod(days: number): UsageStats {
	const stats = getUsageStats()
	const cutoffTime = Date.now() - (days * 24 * 60 * 60 * 1000)
	
	const filteredEntries = stats.entries.filter(entry => entry.timestamp >= cutoffTime)
	
	return {
		totalCubentUnits: filteredEntries.reduce((sum, entry) => sum + entry.cubentUnits, 0),
		totalMessages: filteredEntries.reduce((sum, entry) => sum + entry.messageCount, 0),
		entries: filteredEntries,
		lastUpdated: stats.lastUpdated,
	}
}

// Get usage breakdown by model
export function getUsageByModel(days?: number): Record<string, { cubentUnits: number; messages: number }> {
	const stats = days ? getUsageStatsForPeriod(days) : getUsageStats()
	
	return stats.entries.reduce((breakdown, entry) => {
		if (!breakdown[entry.modelId]) {
			breakdown[entry.modelId] = { cubentUnits: 0, messages: 0 }
		}
		breakdown[entry.modelId].cubentUnits += entry.cubentUnits
		breakdown[entry.modelId].messages += entry.messageCount
		return breakdown
	}, {} as Record<string, { cubentUnits: number; messages: number }>)
}

// Clear all usage data
export function clearUsageStats(): void {
	try {
		localStorage.removeItem(USAGE_STORAGE_KEY)
	} catch (error) {
		console.error("Failed to clear usage stats:", error)
	}
}

// Export usage data as JSON for backup/analysis
export function exportUsageData(): string {
	const stats = getUsageStats()
	return JSON.stringify(stats, null, 2)
}

// Import usage data from JSON
export function importUsageData(jsonData: string): boolean {
	try {
		const imported = JSON.parse(jsonData) as UsageStats
		// Validate the structure
		if (typeof imported.totalCubentUnits === 'number' && 
			typeof imported.totalMessages === 'number' && 
			Array.isArray(imported.entries)) {
			saveUsageStats(imported)
			return true
		}
		return false
	} catch (error) {
		console.error("Failed to import usage data:", error)
		return false
	}
}

// Initialize usage tracking on window object for use in components
export function initializeUsageTracking(): void {
	if (typeof window !== 'undefined') {
		window.usageTracking = {
			getUsageStats,
			getUsageStatsForPeriod,
			getUsageByModel,
			clearUsageStats,
			exportUsageData,
			importUsageData,
		}
	}
}
