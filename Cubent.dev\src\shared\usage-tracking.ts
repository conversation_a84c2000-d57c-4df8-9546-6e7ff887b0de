import { getCubentUnitsForModel } from "./cost"

// Usage tracking data structure
export interface UsageEntry {
	timestamp: number
	modelId: string
	cubentUnits: number
	messageCount: number // Track number of messages sent
	provider: string
	configName: string
}

export interface UsageStats {
	totalCubentUnits: number
	totalMessages: number
	entries: UsageEntry[]
	lastUpdated: number
}

// Local storage key for usage data
const USAGE_STORAGE_KEY = "cubent-usage-stats"

// Get current usage stats from local storage
export function getUsageStats(): UsageStats {
	try {
		const stored = localStorage.getItem(USAGE_STORAGE_KEY)
		if (stored) {
			const parsed = JSON.parse(stored) as UsageStats
			// Ensure the structure is valid
			return {
				totalCubentUnits: parsed.totalCubentUnits || 0,
				totalMessages: parsed.totalMessages || 0,
				entries: Array.isArray(parsed.entries) ? parsed.entries : [],
				lastUpdated: parsed.lastUpdated || Date.now(),
			}
		}
	} catch (error) {
		console.warn("Failed to parse usage stats from localStorage:", error)
	}

	// Return default stats if nothing stored or parsing failed
	return {
		totalCubentUnits: 0,
		totalMessages: 0,
		entries: [],
		lastUpdated: Date.now(),
	}
}

// Save usage stats to local storage
export function saveUsageStats(stats: UsageStats): void {
	try {
		localStorage.setItem(USAGE_STORAGE_KEY, JSON.stringify(stats))
	} catch (error) {
		console.error("Failed to save usage stats to localStorage:", error)
	}
}

// Track a new message usage
export function trackMessageUsage(
	modelId: string,
	provider: string,
	configName: string,
	messageCount: number = 1,
): void {
	const cubentUnits = getCubentUnitsForModel(modelId)
	
	// Only track if the model has Cubent units (non-BYAK models)
	if (cubentUnits > 0) {
		const stats = getUsageStats()
		
		const entry: UsageEntry = {
			timestamp: Date.now(),
			modelId,
			cubentUnits: cubentUnits * messageCount,
			messageCount,
			provider,
			configName,
		}
		
		stats.entries.push(entry)
		stats.totalCubentUnits += entry.cubentUnits
		stats.totalMessages += messageCount
		stats.lastUpdated = Date.now()
		
		// Keep only last 1000 entries to prevent storage bloat
		if (stats.entries.length > 1000) {
			const removedEntries = stats.entries.splice(0, stats.entries.length - 1000)
			// Recalculate totals to maintain accuracy
			stats.totalCubentUnits = stats.entries.reduce((sum, e) => sum + e.cubentUnits, 0)
			stats.totalMessages = stats.entries.reduce((sum, e) => sum + e.messageCount, 0)
		}
		
		saveUsageStats(stats)
	}
}

// Get usage stats for a specific time period
export function getUsageStatsForPeriod(days: number): UsageStats {
	const stats = getUsageStats()
	const cutoffTime = Date.now() - (days * 24 * 60 * 60 * 1000)
	
	const filteredEntries = stats.entries.filter(entry => entry.timestamp >= cutoffTime)
	
	return {
		totalCubentUnits: filteredEntries.reduce((sum, entry) => sum + entry.cubentUnits, 0),
		totalMessages: filteredEntries.reduce((sum, entry) => sum + entry.messageCount, 0),
		entries: filteredEntries,
		lastUpdated: stats.lastUpdated,
	}
}

// Get usage breakdown by model
export function getUsageByModel(days?: number): Record<string, { cubentUnits: number; messages: number }> {
	const stats = days ? getUsageStatsForPeriod(days) : getUsageStats()
	
	return stats.entries.reduce((breakdown, entry) => {
		if (!breakdown[entry.modelId]) {
			breakdown[entry.modelId] = { cubentUnits: 0, messages: 0 }
		}
		breakdown[entry.modelId].cubentUnits += entry.cubentUnits
		breakdown[entry.modelId].messages += entry.messageCount
		return breakdown
	}, {} as Record<string, { cubentUnits: number; messages: number }>)
}

// Clear all usage data
export function clearUsageStats(): void {
	try {
		localStorage.removeItem(USAGE_STORAGE_KEY)
	} catch (error) {
		console.error("Failed to clear usage stats:", error)
	}
}

// Export usage data as JSON for backup/analysis
export function exportUsageData(): string {
	const stats = getUsageStats()
	return JSON.stringify(stats, null, 2)
}

// Import usage data from JSON
export function importUsageData(jsonData: string): boolean {
	try {
		const imported = JSON.parse(jsonData) as UsageStats
		// Validate the structure
		if (typeof imported.totalCubentUnits === 'number' && 
			typeof imported.totalMessages === 'number' && 
			Array.isArray(imported.entries)) {
			saveUsageStats(imported)
			return true
		}
		return false
	} catch (error) {
		console.error("Failed to import usage data:", error)
		return false
	}
}
